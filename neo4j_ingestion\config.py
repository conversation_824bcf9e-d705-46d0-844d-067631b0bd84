"""Configuration management for Neo4j ingestion and OpenAI integration."""

import os
import json
import logging
from typing import Optional, Dict, Any
from openai import OpenAI
from neo4j import GraphDatabase


class Neo4jConfig:
    """Configuration class for Neo4j database connection."""
    
    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j"):
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self._driver = None
        
    def get_driver(self):
        """Get or create Neo4j driver with error handling."""
        if self._driver is None:
            try:
                self._driver = GraphDatabase.driver(
                    self.uri, 
                    auth=(self.username, self.password)
                )
                # Test connection
                with self._driver.session(database=self.database) as session:
                    session.run("RETURN 1")
                logging.info("Successfully connected to Neo4j database")
            except Exception as e:
                logging.error(f"Failed to connect to Neo4j: {e}")
                raise ConnectionError(f"Neo4j connection failed: {e}")
        return self._driver
    
    def close(self):
        """Close the Neo4j driver connection."""
        if self._driver:
            self._driver.close()
            self._driver = None


class OpenAIConfig:
    """Configuration class for OpenAI API integration."""
    
    def __init__(self, api_key: str, embedding_model: str = "text-embedding-3-small", 
                 chat_model: str = "gpt-3.5-turbo"):
        self.api_key = api_key
        self.embedding_model = embedding_model
        self.chat_model = chat_model
        self._client = None
        
    def get_client(self) -> OpenAI:
        """Get or create OpenAI client with error handling."""
        if self._client is None:
            try:
                self._client = OpenAI(api_key=self.api_key)
                # Test connection with a simple embedding request
                test_response = self._client.embeddings.create(
                    input=["test"], 
                    model=self.embedding_model
                )
                logging.info("Successfully connected to OpenAI API")
            except Exception as e:
                logging.error(f"Failed to connect to OpenAI API: {e}")
                raise ConnectionError(f"OpenAI API connection failed: {e}")
        return self._client


class ConfigManager:
    """Main configuration manager for the Neo4j ingestion system."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "neo4j_ingestion/config.json"
        self.neo4j_config = None
        self.openai_config = None
        self._load_config()
        
    def _load_config(self):
        """Load configuration from file or environment variables."""
        config_data = {}
        
        # Try to load from config file first
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                logging.info(f"Loaded configuration from {self.config_file}")
            except Exception as e:
                logging.warning(f"Failed to load config file {self.config_file}: {e}")
        
        # Load Neo4j configuration
        neo4j_config = config_data.get('neo4j', {})
        self.neo4j_config = Neo4jConfig(
            uri=neo4j_config.get('uri') or os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            username=neo4j_config.get('username') or os.getenv('NEO4J_USERNAME', 'neo4j'),
            password=neo4j_config.get('password') or os.getenv('NEO4J_PASSWORD', 'password'),
            database=neo4j_config.get('database') or os.getenv('NEO4J_DATABASE', 'neo4j')
        )
        
        # Load OpenAI configuration
        openai_config = config_data.get('openai', {})
        self.openai_config = OpenAIConfig(
            api_key=openai_config.get('api_key') or os.getenv('OPENAI_API_KEY'),
            embedding_model=openai_config.get('embedding_model') or os.getenv('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small'),
            chat_model=openai_config.get('chat_model') or os.getenv('OPENAI_CHAT_MODEL', 'gpt-3.5-turbo')
        )
        
        if not self.openai_config.api_key:
            raise ValueError("OpenAI API key not found in config file or environment variables")
    
    def get_neo4j_driver(self):
        """Get Neo4j driver."""
        return self.neo4j_config.get_driver()
    
    def get_openai_client(self) -> OpenAI:
        """Get OpenAI client."""
        return self.openai_config.get_client()
    
    def close_connections(self):
        """Close all connections."""
        if self.neo4j_config:
            self.neo4j_config.close()


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('neo4j_ingestion/ingestion.log'),
            logging.StreamHandler()
        ]
    )
